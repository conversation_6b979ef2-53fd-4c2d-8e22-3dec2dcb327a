import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Star, Phone, Globe, MapPin, Clock, ExternalLink, MessageCircle } from "lucide-react"
import { <PERSON> } from "@/lib/supabase"
import Link from "next/link"
import { addDirectoryUTM } from "@/lib/utils/utm"

interface AttorneyProfileProps {
  attorney: Attorney
}

export function AttorneyProfile({ attorney }: AttorneyProfileProps) {
  const googleMapsUrl = attorney.url || `https://www.google.com/search?q=${encodeURIComponent(attorney.title + ' ' + attorney.city + ' ' + attorney.state)}`

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header Section */}
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
          <div className="flex-1">
            <h1 className="text-4xl font-bold mb-4">{attorney.title}</h1>
            
            <div className="flex items-center space-x-4 mb-4">
              {attorney.total_score > 0 && (
                <div className="flex items-center space-x-1">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-5 w-5 ${
                          i < Math.floor(attorney.total_score)
                            ? 'fill-yellow-400 text-yellow-400'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="font-semibold text-lg">{attorney.total_score.toFixed(1)}</span>
                  <span className="text-muted-foreground">
                    ({attorney.reviews_count} reviews)
                  </span>
                </div>
              )}
            </div>

            <div className="space-y-2 text-muted-foreground">
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                <span>{attorney.address}</span>
              </div>
              
              {attorney.phone && (
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 flex-shrink-0" />
                  <a
                    href={`tel:${attorney.phone}`}
                    className="hover:text-primary"
                    data-umami-event="call-click"
                    data-umami-event-phone={attorney.phone}
                  >
                    {attorney.phone}
                  </a>
                </div>
              )}
              
              {attorney.website && (
                <div className="flex items-center">
                  <Globe className="h-4 w-4 mr-2 flex-shrink-0" />
                  <a
                    href={addDirectoryUTM(attorney.website)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:text-primary flex items-center"
                    data-umami-event="link-click"
                    data-umami-event-url={attorney.website}
                  >
                    Visit Website
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-col space-y-3 lg:w-64">
            {attorney.phone && (
              <Button size="lg" className="w-full" asChild>
                <a
                  href={`tel:${attorney.phone}`}
                  data-umami-event="call-click"
                  data-umami-event-phone={attorney.phone}
                >
                  <Phone className="h-4 w-4 mr-2" />
                  Call Now
                </a>
              </Button>
            )}
            
            <Button variant="outline" size="lg" className="w-full" asChild>
              <a href={googleMapsUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                See All Reviews
              </a>
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* About Section */}
          {attorney.seo_snippets && attorney.seo_snippets.length > 0 && (
            <Card>
              <CardHeader>
                <h2 className="text-2xl font-semibold">About {attorney.title}</h2>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  {attorney.seo_snippets.map((snippet, index) => {
                    // Clean up JSON-like text and display as normal text
                    let cleanText = snippet.text
                    try {
                      // Try to parse if it looks like JSON
                      if (cleanText.startsWith('{') || cleanText.startsWith('[')) {
                        const parsed = JSON.parse(cleanText)
                        if (typeof parsed === 'string') {
                          cleanText = parsed
                        } else if (parsed.text) {
                          cleanText = parsed.text
                        } else if (parsed.description) {
                          cleanText = parsed.description
                        }
                      }
                    } catch {
                      // If parsing fails, use original text
                    }

                    return (
                      <p key={snippet.id || index} className="mb-4 text-muted-foreground leading-relaxed">
                        {cleanText}
                      </p>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Services */}
          {attorney.service_keywords && attorney.service_keywords.length > 0 && (
            <Card>
              <CardHeader>
                <h2 className="text-2xl font-semibold">Practice Areas</h2>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {attorney.service_keywords.map((keyword, index) => (
                    <Badge key={index} variant="secondary" className="text-sm py-1 px-3">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* FAQ Section */}
          {attorney.faq_items && attorney.faq_items.length > 0 && (
            <Card>
              <CardHeader>
                <h2 className="text-2xl font-semibold">Frequently Asked Questions</h2>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {attorney.faq_items.map((faq, index) => (
                    <div key={index}>
                      <h3 className="font-semibold text-lg mb-2">{faq.question}</h3>
                      <p className="text-muted-foreground leading-relaxed">{faq.answer}</p>
                      {index < attorney.faq_items!.length - 1 && <Separator className="mt-4" />}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Client Reviews Summary */}
          {attorney.processed_reviews && attorney.processed_reviews.snippets && attorney.processed_reviews.snippets.length > 0 && (
            <Card>
              <CardHeader>
                <h2 className="text-2xl font-semibold">Google Reviews Highlights</h2>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {(() => {
                    // Randomly pick one review snippet to display
                    const snippets = attorney.processed_reviews.snippets
                    const randomIndex = Math.floor(Math.random() * snippets.length)
                    const selectedSnippet = snippets[randomIndex]

                    return (
                      <div className="p-4 bg-muted/50 rounded-lg">
                        <p className="text-muted-foreground leading-relaxed italic">
                          "{selectedSnippet.text}"
                        </p>
                      </div>
                    )
                  })()}
                </div>
                
                <div className="mt-6 pt-4 border-t">
                  <Button variant="outline" className="w-full" asChild>
                    <a href={googleMapsUrl} target="_blank" rel="noopener noreferrer">
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Read All {attorney.reviews_count} Reviews on Google
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Contact Card */}
          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold">Contact Information</h3>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Address</h4>
                <p className="text-sm text-muted-foreground">{attorney.address}</p>
                <p className="text-sm text-muted-foreground">
                  {attorney.city}, {attorney.state} {attorney.postal_code}
                </p>
              </div>
              
              {attorney.phone && (
                <div>
                  <h4 className="font-medium mb-2">Phone</h4>
                  <a
                    href={`tel:${attorney.phone}`}
                    className="text-sm text-primary hover:underline"
                    data-umami-event="call-click"
                    data-umami-event-phone={attorney.phone}
                  >
                    {attorney.phone}
                  </a>
                </div>
              )}
              
              {attorney.website && (
                <div>
                  <h4 className="font-medium mb-2">Website</h4>
                  <a
                    href={addDirectoryUTM(attorney.website)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-primary hover:underline flex items-center"
                    data-umami-event="link-click"
                    data-umami-event-url={attorney.website}
                  >
                    Visit Website
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Hours */}
          {attorney.opening_hours && (
            <Card>
              <CardHeader>
                <h3 className="text-xl font-semibold">Hours</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Array.isArray(attorney.opening_hours) ? (
                    attorney.opening_hours.map((schedule: any, index: number) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span className="font-medium">{schedule.day}</span>
                        <span className="text-muted-foreground">{schedule.hours}</span>
                      </div>
                    ))
                  ) : (
                    Object.entries(attorney.opening_hours).map(([day, hours]) => (
                      <div key={day} className="flex justify-between text-sm">
                        <span className="font-medium">{day}</span>
                        <span className="text-muted-foreground">{hours as string}</span>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Rating Summary */}
          {attorney.reviews_count > 0 && (
            <Card>
              <CardHeader>
                <h3 className="text-xl font-semibold">Rating Summary</h3>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold mb-2">{attorney.total_score.toFixed(1)}</div>
                  <div className="flex justify-center mb-2">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < Math.floor(attorney.total_score)
                            ? 'fill-yellow-400 text-yellow-400'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Based on {attorney.reviews_count} reviews
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
