"use client"

import type React from "react"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, Phone, Globe, MapPin, Clock, Award, ExternalLink } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { Attorney } from "@/lib/supabase"
import { generateStateSlug, generateCitySlug, generateAttorneySlug } from "@/lib/utils/seo"
import { addDirectoryUTM } from "@/lib/utils/utm"

interface LawyerCardProps {
  lawyer: Attorney
}

export function LawyerCard({ lawyer }: LawyerCardProps) {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
      />
    ))
  }

  const isOpen24Hours = lawyer.opening_hours &&
    typeof lawyer.opening_hours === 'object' &&
    Object.values(lawyer.opening_hours).every((hours) => hours === "Open 24 hours")

  const handlePhoneClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (lawyer.phone) {
      window.location.href = `tel:${lawyer.phone}`
    }
  }

  const handleWebsiteClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (lawyer.website) {
      window.open(lawyer.website, "_blank", "noopener,noreferrer")
    }
  }

  const googleMapsUrl = lawyer.url || `https://www.google.com/search?q=${encodeURIComponent(lawyer.title + ' ' + lawyer.city + ' ' + lawyer.state)}`

  return (
    <Link href={`/${generateStateSlug(lawyer.state)}/${generateCitySlug(lawyer.city)}/${generateAttorneySlug(lawyer)}`} className="block h-full">
      <Card className="h-full flex flex-col hover:shadow-lg transition-shadow duration-200 cursor-pointer">
        <CardHeader className="pb-4">
          <div className="relative h-48 w-full mb-4 rounded-lg overflow-hidden">
            <Image src={lawyer.image_url || "/placeholder.svg"} alt={lawyer.title} fill className="object-cover" />
            <div className="absolute top-2 right-2">
              <Badge variant="secondary" className="bg-primary text-primary-foreground">
                <Award className="h-3 w-3 mr-1" />
                Top Rated
              </Badge>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="font-bold text-xl leading-tight line-clamp-2">{lawyer.title}</h3>

            <div className="flex items-center space-x-2">
              <div className="flex items-center">{renderStars(lawyer.total_score)}</div>
              <span className="font-semibold text-lg">{lawyer.total_score}</span>
              <span className="text-muted-foreground text-base">({lawyer.reviews_count} reviews)</span>
            </div>

            <div className="flex items-center text-muted-foreground">
              <MapPin className="h-5 w-5 mr-1" />
              <span className="text-base">
                {lawyer.city}, {lawyer.state}
              </span>
            </div>

            {isOpen24Hours && (
              <div className="flex items-center text-green-600">
                <Clock className="h-5 w-5 mr-1" />
                <span className="text-base font-medium">Available 24/7</span>
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent className="flex-1">
          <p className="text-base text-muted-foreground mb-4 line-clamp-3">
            {lawyer.seo_snippets?.[0]?.text ||
             lawyer.processed_reviews?.snippets?.[0]?.text ||
             `Experienced car accident attorney in ${lawyer.city}, ${lawyer.state}. Contact for a free consultation.`}
          </p>

          <div className="flex flex-wrap gap-2">
            {(lawyer.service_keywords || []).slice(0, 3).map((keyword, index) => (
              <Badge key={index} variant="outline" className="text-sm">
                {keyword}
              </Badge>
            ))}
          </div>
        </CardContent>

        <CardFooter className="pt-4 space-y-2">
          <div className="flex space-x-2 w-full">
            {lawyer.phone && (
              <Button
                variant="outline"
                size="sm"
                className="flex-1 bg-transparent"
                onClick={handlePhoneClick}
                data-umami-event="call-click"
                data-umami-event-phone={lawyer.phone}
              >
                <Phone className="h-4 w-4 mr-1" />
                Call Now
              </Button>
            )}

            <Button variant="outline" size="sm" className="flex-1 bg-transparent" asChild>
              <a href={googleMapsUrl} target="_blank" rel="noopener noreferrer" onClick={(e) => e.stopPropagation()}>
                <ExternalLink className="h-4 w-4 mr-1" />
                Reviews
              </a>
            </Button>
          </div>
        </CardFooter>
      </Card>
    </Link>
  )
}
