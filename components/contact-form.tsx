"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Send, CheckCircle } from "lucide-react"

export function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    state: '',
    subject: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    setIsSubmitting(false)
    setIsSubmitted(true)
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false)
      setFormData({
        name: '',
        email: '',
        phone: '',
        state: '',
        subject: '',
        message: ''
      })
    }, 3000)
  }

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  if (isSubmitted) {
    return (
      <div className="text-center py-8">
        <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold mb-2">Message Sent!</h3>
        <p className="text-muted-foreground">
          Thank you for contacting us. We'll get back to you within 24 hours.
        </p>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Full Name *</Label>
          <Input
            id="name"
            type="text"
            placeholder="Your full name"
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="email">Email Address *</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={formData.email}
            onChange={(e) => handleChange('email', e.target.value)}
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            type="tel"
            placeholder="(*************"
            value={formData.phone}
            onChange={(e) => handleChange('phone', e.target.value)}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="state">State</Label>
          <Select value={formData.state} onValueChange={(value) => handleChange('state', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select your state" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="alabama">Alabama</SelectItem>
              <SelectItem value="alaska">Alaska</SelectItem>
              <SelectItem value="arizona">Arizona</SelectItem>
              <SelectItem value="arkansas">Arkansas</SelectItem>
              <SelectItem value="california">California</SelectItem>
              <SelectItem value="colorado">Colorado</SelectItem>
              <SelectItem value="connecticut">Connecticut</SelectItem>
              <SelectItem value="delaware">Delaware</SelectItem>
              <SelectItem value="florida">Florida</SelectItem>
              <SelectItem value="georgia">Georgia</SelectItem>
              <SelectItem value="hawaii">Hawaii</SelectItem>
              <SelectItem value="idaho">Idaho</SelectItem>
              <SelectItem value="illinois">Illinois</SelectItem>
              <SelectItem value="indiana">Indiana</SelectItem>
              <SelectItem value="iowa">Iowa</SelectItem>
              <SelectItem value="kansas">Kansas</SelectItem>
              <SelectItem value="kentucky">Kentucky</SelectItem>
              <SelectItem value="louisiana">Louisiana</SelectItem>
              <SelectItem value="maine">Maine</SelectItem>
              <SelectItem value="maryland">Maryland</SelectItem>
              <SelectItem value="massachusetts">Massachusetts</SelectItem>
              <SelectItem value="michigan">Michigan</SelectItem>
              <SelectItem value="minnesota">Minnesota</SelectItem>
              <SelectItem value="mississippi">Mississippi</SelectItem>
              <SelectItem value="missouri">Missouri</SelectItem>
              <SelectItem value="montana">Montana</SelectItem>
              <SelectItem value="nebraska">Nebraska</SelectItem>
              <SelectItem value="nevada">Nevada</SelectItem>
              <SelectItem value="new-hampshire">New Hampshire</SelectItem>
              <SelectItem value="new-jersey">New Jersey</SelectItem>
              <SelectItem value="new-mexico">New Mexico</SelectItem>
              <SelectItem value="new-york">New York</SelectItem>
              <SelectItem value="north-carolina">North Carolina</SelectItem>
              <SelectItem value="north-dakota">North Dakota</SelectItem>
              <SelectItem value="ohio">Ohio</SelectItem>
              <SelectItem value="oklahoma">Oklahoma</SelectItem>
              <SelectItem value="oregon">Oregon</SelectItem>
              <SelectItem value="pennsylvania">Pennsylvania</SelectItem>
              <SelectItem value="rhode-island">Rhode Island</SelectItem>
              <SelectItem value="south-carolina">South Carolina</SelectItem>
              <SelectItem value="south-dakota">South Dakota</SelectItem>
              <SelectItem value="tennessee">Tennessee</SelectItem>
              <SelectItem value="texas">Texas</SelectItem>
              <SelectItem value="utah">Utah</SelectItem>
              <SelectItem value="vermont">Vermont</SelectItem>
              <SelectItem value="virginia">Virginia</SelectItem>
              <SelectItem value="washington">Washington</SelectItem>
              <SelectItem value="west-virginia">West Virginia</SelectItem>
              <SelectItem value="wisconsin">Wisconsin</SelectItem>
              <SelectItem value="wyoming">Wyoming</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="subject">Subject *</Label>
        <Select value={formData.subject} onValueChange={(value) => handleChange('subject', value)} required>
          <SelectTrigger>
            <SelectValue placeholder="Select a subject" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="find-attorney">Help Finding an Attorney</SelectItem>
            <SelectItem value="legal-question">Legal Question</SelectItem>
            <SelectItem value="case-evaluation">Case Evaluation</SelectItem>
            <SelectItem value="website-issue">Website Issue</SelectItem>
            <SelectItem value="other">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="message">Message *</Label>
        <Textarea
          id="message"
          placeholder="Please describe how we can help you..."
          rows={5}
          value={formData.message}
          onChange={(e) => handleChange('message', e.target.value)}
          required
        />
      </div>

      <Button type="submit" className="w-full" disabled={isSubmitting}>
        {isSubmitting ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Sending...
          </>
        ) : (
          <>
            <Send className="h-4 w-4 mr-2" />
            Send Message
          </>
        )}
      </Button>
      
      <p className="text-xs text-muted-foreground text-center">
        By submitting this form, you agree to our Privacy Policy and Terms of Service.
      </p>
    </form>
  )
}
