import { Metadata } from 'next'
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { SearchForm } from "@/components/search-form"
import { LawyerCard } from "@/components/lawyer-card"
import { PaginationComponent } from "@/components/pagination"
import { getAttorneys, getStates } from "@/lib/supabase"
import { getRecordsPerPage } from "@/lib/utils/pagination"

interface SearchPageProps {
  searchParams: Promise<{
    q?: string
    city?: string
    state?: string
    page?: string
  }>
}

export const metadata: Metadata = {
  title: 'Search Car Accident Attorneys | Find Lawyers Near You',
  description: 'Search for car accident attorneys by location, name, or specialty. Find qualified personal injury lawyers with verified reviews and ratings.',
  alternates: {
    canonical: '/search',
  },
}
export default async function SearchPage({ searchParams }: SearchPageProps) {
  const params = await searchParams
  const query = params.q || ''
  const city = params.city || ''
  const state = params.state || ''
  const currentPage = parseInt(params.page || '1', 10)
  const recordsPerPage = getRecordsPerPage()

  // Get search results
  const { attorneys, total, error } = await getAttorneys(
    currentPage,
    recordsPerPage,
    city,
    state,
    query
  )

  // Get states for filter dropdown
  const { states } = await getStates()

  const totalPages = Math.ceil(total / recordsPerPage)
  const hasResults = attorneys.length > 0
  const hasFilters = query || city || state

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-5xl font-bold mb-4">
            {hasFilters ? 'Search Results' : 'Find Car Accident Attorneys'}
          </h1>
          <p className="text-xl text-muted-foreground">
            {hasFilters
              ? `Found ${total} attorneys matching your search criteria`
              : 'Search for qualified car accident attorneys by location, name, or specialty'
            }
          </p>
        </div>

        {/* Search Form */}
        <div className="mb-8">
          <SearchForm
            initialQuery={query}
            initialCity={city}
            initialState={state}
            states={states}
          />
        </div>
        {error && (
          <div className="mb-8 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
            <p className="text-destructive">Error loading search results: {error}</p>
          </div>
        )}

        {hasFilters && (
          <div className="mb-6">
            <div className="flex flex-wrap items-center gap-2 text-sm">
              <span className="text-muted-foreground">Active filters:</span>
              {query && (
                <span className="px-2 py-1 bg-primary/10 text-primary rounded">
                  Search: "{query}"
                </span>
              )}
              {city && (
                <span className="px-2 py-1 bg-primary/10 text-primary rounded">
                  City: {city}
                </span>
              )}
              {state && (
                <span className="px-2 py-1 bg-primary/10 text-primary rounded">
                  State: {state}
                </span>
              )}
            </div>
          </div>
        )}

        {hasResults ? (
          <>
            {/* Results summary */}
            <div className="mb-6">
              <p className="text-muted-foreground">
                Showing {(currentPage - 1) * recordsPerPage + 1}-{Math.min(currentPage * recordsPerPage, total)} of {total} attorneys
              </p>
            </div>

            {/* Results grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {attorneys.map((attorney) => (
                <LawyerCard key={attorney.source_id} lawyer={attorney} />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <PaginationComponent
                currentPage={currentPage}
                totalPages={totalPages}
                baseUrl="/search"
              />
            )}
          </>
        ) : hasFilters ? (
          <div className="text-center py-12">
            <h2 className="text-2xl font-semibold mb-4">No attorneys found</h2>
            <p className="text-muted-foreground mb-6">
              Try adjusting your search criteria or browse all attorneys.
            </p>
            <SearchForm
              initialQuery=""
              initialCity=""
              initialState=""
              states={states}
              showClearButton={false}
            />
          </div>
        ) : (
          <div className="text-center py-12">
            <h2 className="text-2xl font-semibold mb-4">Start Your Search</h2>
            <p className="text-muted-foreground">
              Use the search form above to find car accident attorneys in your area.
            </p>
          </div>
        )}

        {/* SEO Content */}
        {!hasFilters && (
          <section className="mt-16 prose max-w-none">
            <h2>How to Find the Right Car Accident Attorney</h2>
            <p>
              Finding the right car accident attorney is crucial for getting the compensation you deserve.
              Our directory makes it easy to search and compare qualified attorneys in your area.
            </p>

            <h3>What to Consider When Choosing an Attorney</h3>
            <ul>
              <li><strong>Experience:</strong> Look for attorneys who specialize in car accident cases</li>
              <li><strong>Track Record:</strong> Check their success rate with similar cases</li>
              <li><strong>Reviews:</strong> Read client testimonials and ratings</li>
              <li><strong>Location:</strong> Choose someone familiar with local laws and courts</li>
              <li><strong>Communication:</strong> Ensure they're responsive and keep you informed</li>
            </ul>

            <h3>Types of Car Accident Cases</h3>
            <p>
              Car accident attorneys handle various types of cases including:
            </p>
            <ul>
              <li>Rear-end collisions</li>
              <li>Intersection accidents</li>
              <li>Highway accidents</li>
              <li>Drunk driving accidents</li>
              <li>Distracted driving cases</li>
              <li>Hit and run accidents</li>
            </ul>
          </section>
        )}
      </main>

      <Footer />
    </div>
  )
}
